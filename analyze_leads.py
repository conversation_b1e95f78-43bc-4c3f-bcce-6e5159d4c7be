#!/usr/bin/env python3
"""
Lead Data Analysis Script
Analyzes data coverage and completeness for Hunter.io leads
"""

import json
from collections import defaultdict, Counter
from hunter_client import HunterClient, HunterAPIError


def analyze_domain_search_data():
    """Analyze the domain search data for comprehensive coverage."""
    
    client = HunterClient()
    
    try:
        # Get 100 leads from Vaisala
        result = client.domain_search('vaisala.com', limit=100)
        data = result.get('data', {})
        people = data.get('emails', [])
        
        print("="*80)
        print("VAISALA LEADS DATA ANALYSIS - 100 LEADS")
        print("="*80)
        
        # Basic statistics
        print(f"\n📊 BASIC STATISTICS")
        print(f"Total leads found: {len(people)}")
        print(f"Company: {data.get('organization', 'N/A')}")
        print(f"Domain: {data.get('domain', 'N/A')}")
        print(f"Industry: {data.get('industry', 'N/A')}")
        print(f"Company size: {data.get('headcount', 'N/A')}")
        print(f"Country: {data.get('country', 'N/A')}")
        
        # Analyze data completeness for each property
        properties_analysis = {
            'first_name': {'available': 0, 'missing': 0, 'values': []},
            'last_name': {'available': 0, 'missing': 0, 'values': []},
            'email': {'available': 0, 'missing': 0, 'values': []},
            'position': {'available': 0, 'missing': 0, 'values': []},
            'department': {'available': 0, 'missing': 0, 'values': []},
            'seniority': {'available': 0, 'missing': 0, 'values': []},
            'confidence': {'available': 0, 'missing': 0, 'values': []},
            'linkedin': {'available': 0, 'missing': 0, 'values': []},
            'twitter': {'available': 0, 'missing': 0, 'values': []},
            'phone_number': {'available': 0, 'missing': 0, 'values': []},
            'sources': {'available': 0, 'missing': 0, 'values': []},
        }
        
        # Analyze each person
        for person in people:
            for prop in properties_analysis:
                if prop in person and person[prop] and person[prop] != '':
                    properties_analysis[prop]['available'] += 1
                    properties_analysis[prop]['values'].append(person[prop])
                else:
                    properties_analysis[prop]['missing'] += 1
        
        # Display property analysis
        print(f"\n📋 DATA COMPLETENESS ANALYSIS")
        print(f"{'Property':<15} {'Available':<10} {'Missing':<10} {'Coverage %':<12} {'Sample Values'}")
        print("-" * 80)
        
        for prop, stats in properties_analysis.items():
            total = stats['available'] + stats['missing']
            coverage = (stats['available'] / total * 100) if total > 0 else 0
            
            # Get sample values
            sample_values = []
            if stats['values']:
                if isinstance(stats['values'][0], (list, dict)):
                    sample_values = [f"{len(stats['values'])} items"]
                else:
                    sample_values = list(set(stats['values'][:3]))  # First 3 unique values
            
            sample_str = ', '.join(str(v)[:30] for v in sample_values[:2])
            if len(sample_str) > 40:
                sample_str = sample_str[:40] + "..."
            
            print(f"{prop:<15} {stats['available']:<10} {stats['missing']:<10} {coverage:<11.1f}% {sample_str}")
        
        # Department analysis
        departments = [p.get('department', 'Unknown') for p in people if p.get('department')]
        dept_counts = Counter(departments)
        
        print(f"\n🏢 DEPARTMENT DISTRIBUTION")
        print(f"{'Department':<20} {'Count':<8} {'Percentage'}")
        print("-" * 40)
        for dept, count in dept_counts.most_common():
            percentage = (count / len(people)) * 100
            print(f"{dept:<20} {count:<8} {percentage:.1f}%")
        
        # Seniority analysis
        seniorities = [p.get('seniority', 'Unknown') for p in people if p.get('seniority')]
        seniority_counts = Counter(seniorities)
        
        print(f"\n👔 SENIORITY DISTRIBUTION")
        print(f"{'Seniority':<20} {'Count':<8} {'Percentage'}")
        print("-" * 40)
        for seniority, count in seniority_counts.most_common():
            percentage = (count / len(people)) * 100
            print(f"{seniority:<20} {count:<8} {percentage:.1f}%")
        
        # Confidence analysis
        confidences = [p.get('confidence', 0) for p in people if p.get('confidence')]
        if confidences:
            avg_confidence = sum(confidences) / len(confidences)
            min_confidence = min(confidences)
            max_confidence = max(confidences)
            
            print(f"\n🎯 CONFIDENCE ANALYSIS")
            print(f"Average confidence: {avg_confidence:.1f}%")
            print(f"Minimum confidence: {min_confidence}%")
            print(f"Maximum confidence: {max_confidence}%")
            
            # Confidence distribution
            confidence_ranges = {
                '90-100%': len([c for c in confidences if c >= 90]),
                '80-89%': len([c for c in confidences if 80 <= c < 90]),
                '70-79%': len([c for c in confidences if 70 <= c < 80]),
                '<70%': len([c for c in confidences if c < 70])
            }
            
            print(f"\nConfidence Distribution:")
            for range_name, count in confidence_ranges.items():
                percentage = (count / len(confidences)) * 100
                print(f"  {range_name}: {count} leads ({percentage:.1f}%)")
        
        # Position analysis
        positions = [p.get('position', 'Unknown') for p in people if p.get('position')]
        position_keywords = defaultdict(int)
        
        for position in positions:
            words = position.lower().split()
            for word in words:
                if len(word) > 3:  # Only count meaningful words
                    position_keywords[word] += 1
        
        print(f"\n💼 TOP POSITION KEYWORDS")
        print(f"{'Keyword':<20} {'Frequency'}")
        print("-" * 30)
        for keyword, freq in sorted(position_keywords.items(), key=lambda x: x[1], reverse=True)[:10]:
            print(f"{keyword:<20} {freq}")
        
        # LinkedIn coverage
        linkedin_available = len([p for p in people if p.get('linkedin')])
        linkedin_coverage = (linkedin_available / len(people)) * 100
        
        print(f"\n🔗 SOCIAL MEDIA COVERAGE")
        print(f"LinkedIn profiles available: {linkedin_available}/{len(people)} ({linkedin_coverage:.1f}%)")
        
        # Sources analysis
        all_sources = []
        for person in people:
            if person.get('sources'):
                for source in person['sources']:
                    if source.get('domain'):
                        all_sources.append(source['domain'])
        
        source_counts = Counter(all_sources)
        print(f"\n📊 DATA SOURCES")
        print(f"{'Source':<20} {'Count'}")
        print("-" * 30)
        for source, count in source_counts.most_common(5):
            print(f"{source:<20} {count}")
        
        return {
            'total_leads': len(people),
            'properties_analysis': properties_analysis,
            'departments': dept_counts,
            'seniorities': seniority_counts,
            'avg_confidence': avg_confidence if confidences else 0,
            'linkedin_coverage': linkedin_coverage
        }
        
    except HunterAPIError as e:
        print(f"API Error: {e}")
        return None
    except Exception as e:
        print(f"Error: {e}")
        return None


if __name__ == "__main__":
    analyze_domain_search_data()
