#!/usr/bin/env python3
"""
Demo script for Hunter.io CLI Tool
This script demonstrates all the features of the Hunter CLI tool.
"""

import subprocess
import sys
import time

def run_command(cmd, description):
    """Run a command and display the output."""
    print(f"\n{'='*60}")
    print(f"DEMO: {description}")
    print(f"Command: {cmd}")
    print(f"{'='*60}")
    
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=30)
        print(result.stdout)
        if result.stderr:
            print("STDERR:", result.stderr)
        return result.returncode == 0
    except subprocess.TimeoutExpired:
        print("Command timed out")
        return False
    except Exception as e:
        print(f"Error running command: {e}")
        return False

def main():
    """Run the demo."""
    print("Hunter.io CLI Tool - Complete Demo")
    print("This demo will show all features of the Hunter CLI tool")
    print("\nPress Enter to continue or Ctrl+C to exit...")
    
    try:
        input()
    except KeyboardInterrupt:
        print("\nDemo cancelled.")
        sys.exit(0)
    
    # Demo commands
    demos = [
        ("python hunter_cli.py --help", "Show main help"),
        ("python hunter_cli.py search --help", "Show search command help"),
        ("python hunter_cli.py search vaisala.com --limit 3", "Search for people at Vaisala (limit 3)"),
        ("python hunter_cli.py company vaisala.com", "Get company information for Vaisala"),
        ("python hunter_cli.py find-email vaisala.com Lorenzo Gulli", "Find email for Lorenzo Gulli at Vaisala"),
        ("python hunter_cli.<NAME_EMAIL>", "Get detailed information about Lorenzo Gulli"),
        ("python hunter_cli.<NAME_EMAIL>", "Verify Lorenzo Gulli's email"),
    ]
    
    success_count = 0
    
    for cmd, description in demos:
        if run_command(cmd, description):
            success_count += 1
        
        print("\nPress Enter to continue to next demo...")
        try:
            input()
        except KeyboardInterrupt:
            print("\nDemo cancelled.")
            break
    
    print(f"\n{'='*60}")
    print(f"DEMO COMPLETE")
    print(f"Successfully ran {success_count}/{len(demos)} commands")
    print(f"{'='*60}")
    
    print("\nTo use the tool interactively, try:")
    print("python hunter_cli.py search vaisala.com --interactive")

if __name__ == "__main__":
    main()
