"""Configuration settings for Hunter.io CLI tool."""

import os
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Hunter.io API configuration
HUNTER_API_KEY = os.getenv('HUNTER_API_KEY', '****************************************')
HUNTER_BASE_URL = 'https://api.hunter.io/v2'

# API endpoints
ENDPOINTS = {
    'domain_search': f'{HUNTER_BASE_URL}/domain-search',
    'email_finder': f'{HUNTER_BASE_URL}/email-finder',
    'email_verifier': f'{HUNTER_BASE_URL}/email-verifier',
    'person_enrichment': f'{HUNTER_BASE_URL}/people/find',
    'company_enrichment': f'{HUNTER_BASE_URL}/companies/find',
    'combined_enrichment': f'{HUNTER_BASE_URL}/combined/find'
}

# Default settings
DEFAULT_LIMIT = 50
DEFAULT_OFFSET = 0
