# Hunter.io CLI Tool for Enterprise People Search

A powerful command-line interface for finding people within enterprises and retrieving detailed employee information using the Hunter.io API.

## Features

- 🔍 **Domain Search**: Find all people associated with a company domain
- 👤 **Person Details**: Get comprehensive information about specific employees
- 📧 **Email Finder**: Find email addresses for specific people
- ✅ **Email Verification**: Verify email address validity
- 🏢 **Company Information**: Get detailed company data
- 🎯 **Interactive Mode**: Browse and select employees interactively
- 🎨 **Colored Output**: Beautiful, easy-to-read terminal output

## Installation

1. **Clone or download the project files**

2. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

3. **Set up your API key**:
   - Copy `.env.example` to `.env`
   - Add your Hunter.io API key to the `.env` file
   - Or set the `HUNTER_API_KEY` environment variable

## Usage

### Basic Commands

#### 1. Search for People in a Company
```bash
python hunter_cli.py search vaisala.com
```

**With filters:**
```bash
python hunter_cli.py search vaisala.com --department "engineering" --limit 25
python hunter_cli.py search vaisala.com --seniority "senior" --limit 50
```

**Interactive mode (recommended):**
```bash
python hunter_cli.py search vaisala.com --interactive
```

#### 2. Get Detailed Person Information
```bash
python hunter_cli.<NAME_EMAIL>
```

#### 3. Find Email for Specific Person
```bash
python hunter_cli.py find-email vaisala.com John Doe
```

#### 4. Verify Email Address
```bash
python hunter_cli.<NAME_EMAIL>
```

#### 5. Get Company Information
```bash
python hunter_cli.py company vaisala.com
```

### Example Workflow

1. **Search for people in a company:**
   ```bash
   python hunter_cli.py search vaisala.com --interactive --limit 30
   ```

2. **Select employees from the interactive list to get detailed information**

3. **Or get details directly if you have an email:**
   ```bash
   python hunter_cli.<NAME_EMAIL>
   ```

## Command Reference

### `search <domain>`
Search for people in a company domain.

**Options:**
- `--limit, -l`: Number of results (default: 50, max: 100)
- `--department, -d`: Filter by department
- `--seniority, -s`: Filter by seniority level
- `--interactive, -i`: Enable interactive mode for selecting employees

**Example:**
```bash
python hunter_cli.py search stripe.com --limit 25 --department "engineering" --interactive
```

### `details <email>`
Get detailed information about a person by email.

**Example:**
```bash
python hunter_cli.<NAME_EMAIL>
```

### `find-email <domain> <first_name> <last_name>`
Find email address for a specific person.

**Example:**
```bash
python hunter_cli.py find-email stripe.com Patrick Collison
```

### `verify <email>`
Verify an email address.

**Example:**
```bash
python hunter_cli.<NAME_EMAIL>
```

### `company <domain>`
Get detailed information about a company.

**Example:**
```bash
python hunter_cli.py company stripe.com
```

## Output Examples

### Domain Search Output
```
ℹ Searching for people in domain: vaisala.com

Company: Vaisala
Domain: vaisala.com
Industry: Environmental Monitoring

✓ Found 45 people:
┌───┬─────────────────┬──────────────────────────┬─────────────────────┬──────────────┬────────────┐
│ # │ Name            │ Email                    │ Position            │ Department   │ Confidence │
├───┼─────────────────┼──────────────────────────┼─────────────────────┼──────────────┼────────────┤
│ 1 │ John Smith      │ <EMAIL>   │ Software Engineer   │ Engineering  │ 95         │
│ 2 │ Jane Doe        │ <EMAIL>     │ Product Manager     │ Product      │ 92         │
└───┴─────────────────┴──────────────────────────┴─────────────────────┴──────────────┴────────────┘
```

### Person Details Output
```
============================================================
PERSON DETAILS
============================================================

Personal Information:
  Name: John Smith
  Email: <EMAIL>
  Phone: +***********-789

Professional Information:
  Position: Senior Software Engineer
  Department: Engineering
  Seniority: Senior

Company Information:
  Name: Vaisala
  Domain: vaisala.com
  Industry: Environmental Monitoring
  Size: 1000-5000

Social Media:
  LinkedIn: https://linkedin.com/in/johnsmith
```

## API Rate Limits

Hunter.io has different rate limits based on your plan:
- **Free**: 25 requests/month
- **Starter**: 1,000 requests/month
- **Growth**: 5,000 requests/month
- **Business**: 20,000 requests/month

## Error Handling

The tool includes comprehensive error handling for:
- Invalid API keys
- Rate limit exceeded
- Network connectivity issues
- Invalid email formats
- Domain not found

## Requirements

- Python 3.7+
- Hunter.io API key
- Internet connection

## Dependencies

- `requests`: HTTP library for API calls
- `click`: Command-line interface framework
- `tabulate`: Table formatting
- `colorama`: Cross-platform colored terminal output
- `python-dotenv`: Environment variable management

## License

This tool is provided as-is for educational and business purposes. Make sure to comply with Hunter.io's terms of service and applicable data protection regulations.
