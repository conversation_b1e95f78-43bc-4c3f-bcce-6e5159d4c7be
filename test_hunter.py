#!/usr/bin/env python3
"""Test script for Hunter.io CLI tool."""

import sys
import os
from hunter_client import HunterClient, HunterAPIError


def test_api_connection():
    """Test basic API connection."""
    print("Testing Hunter.io API connection...")
    
    try:
        client = HunterClient()
        
        # Test with a well-known domain
        result = client.domain_search("stripe.com", limit=1)
        
        if result and 'data' in result:
            print("✓ API connection successful!")
            print(f"✓ Found data for stripe.com")
            return True
        else:
            print("✗ API connection failed - no data returned")
            return False
            
    except HunterAPIError as e:
        print(f"✗ API Error: {e}")
        return False
    except Exception as e:
        print(f"✗ Unexpected error: {e}")
        return False


def test_email_verification():
    """Test email verification functionality."""
    print("\nTesting email verification...")
    
    try:
        client = HunterClient()
        
        # Test with a known valid email format
        result = client.email_verifier("<EMAIL>")
        
        if result and 'data' in result:
            print("✓ Email verification working!")
            return True
        else:
            print("✗ Email verification failed")
            return False
            
    except HunterAPIError as e:
        print(f"✗ API Error: {e}")
        return False
    except Exception as e:
        print(f"✗ Unexpected error: {e}")
        return False


def main():
    """Run all tests."""
    print("Hunter.io CLI Tool - Test Suite")
    print("=" * 40)
    
    # Check if API key is configured
    from config import HUNTER_API_KEY
    if not HUNTER_API_KEY or HUNTER_API_KEY == 'your_hunter_api_key_here':
        print("✗ API key not configured!")
        print("Please set your Hunter.io API key in config.py or .env file")
        sys.exit(1)
    
    print(f"✓ API key configured: {HUNTER_API_KEY[:8]}...")
    
    # Run tests
    tests_passed = 0
    total_tests = 2
    
    if test_api_connection():
        tests_passed += 1
    
    if test_email_verification():
        tests_passed += 1
    
    # Summary
    print("\n" + "=" * 40)
    print(f"Tests passed: {tests_passed}/{total_tests}")
    
    if tests_passed == total_tests:
        print("✓ All tests passed! The tool is ready to use.")
        sys.exit(0)
    else:
        print("✗ Some tests failed. Please check your configuration.")
        sys.exit(1)


if __name__ == "__main__":
    main()
