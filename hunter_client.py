"""Hunter.io API client for enterprise people search."""

import requests
import json
from typing import Dict, List, Optional, Any
from config import HUNTER_API_KEY, ENDPOINTS


class HunterAPIError(Exception):
    """Custom exception for Hunter.io API errors."""
    pass


class HunterClient:
    """Client for interacting with Hunter.io API."""

    def __init__(self, api_key: str = HUNTER_API_KEY):
        """Initialize the Hunter client with API key."""
        self.api_key = api_key
        self.session = requests.Session()

    def _make_request(self, endpoint: str, params: Dict[str, Any]) -> Dict[str, Any]:
        """Make a request to the Hunter.io API."""
        params['api_key'] = self.api_key

        try:
            response = self.session.get(endpoint, params=params)
            response.raise_for_status()

            data = response.json()

            # Check for API errors
            if isinstance(data, dict) and 'errors' in data:
                errors = data['errors']
                if isinstance(errors, list) and errors:
                    raise HunterAPIError(f"API Error: {errors[0].get('details', 'Unknown error')}")
                else:
                    raise HunterAPIError(f"API Error: {errors}")

            return data

        except requests.exceptions.HTTPError as e:
            if response.status_code == 401:
                raise HunterAPIError("Invalid API key. Please check your Hunter.io API key.")
            elif response.status_code == 429:
                raise HunterAPIError("Rate limit exceeded. Please wait before making more requests.")
            else:
                raise HunterAPIError(f"HTTP Error {response.status_code}: {str(e)}")
        except requests.exceptions.RequestException as e:
            raise HunterAPIError(f"Request failed: {str(e)}")
        except json.JSONDecodeError as e:
            raise HunterAPIError(f"Invalid JSON response: {str(e)}")

    def domain_search(self, domain: str, limit: int = 50, offset: int = 0,
                     department: Optional[str] = None, seniority: Optional[str] = None) -> Dict[str, Any]:
        """Search for people in a company domain."""
        params = {
            'domain': domain,
            'limit': limit,
            'offset': offset
        }

        if department:
            params['department'] = department
        if seniority:
            params['seniority'] = seniority

        return self._make_request(ENDPOINTS['domain_search'], params)

    def email_finder(self, domain: str, first_name: str, last_name: str) -> Dict[str, Any]:
        """Find email address for a specific person."""
        params = {
            'domain': domain,
            'first_name': first_name,
            'last_name': last_name
        }

        return self._make_request(ENDPOINTS['email_finder'], params)

    def email_verifier(self, email: str) -> Dict[str, Any]:
        """Verify an email address."""
        params = {'email': email}
        return self._make_request(ENDPOINTS['email_verifier'], params)

    def person_enrichment(self, email: str) -> Dict[str, Any]:
        """Get detailed information about a person by email."""
        params = {'email': email}
        return self._make_request(ENDPOINTS['person_enrichment'], params)

    def company_enrichment(self, domain: str) -> Dict[str, Any]:
        """Get detailed information about a company."""
        params = {'domain': domain}
        return self._make_request(ENDPOINTS['company_enrichment'], params)

    def combined_enrichment(self, email: str) -> Dict[str, Any]:
        """Get combined person and company information."""
        params = {'email': email}
        return self._make_request(ENDPOINTS['combined_enrichment'], params)
