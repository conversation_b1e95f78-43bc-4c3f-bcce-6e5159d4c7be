#!/usr/bin/env python3
"""
Hunter.io CLI Tool for Enterprise People Search

This tool allows you to search for people within enterprises and get detailed
information about selected employees using the Hunter.io API.
"""

import click
import sys
from typing import List, Dict, Any, Optional
from tabulate import tabulate
from colorama import init, Fore, Style
from hunter_client import HunterClient, HunterAPIError

# Initialize colorama for cross-platform colored output
init(autoreset=True)


class HunterCLI:
    """Command line interface for Hunter.io API."""

    def __init__(self):
        """Initialize the CLI with Hunter client."""
        self.client = HunterClient()

    def print_success(self, message: str):
        """Print success message in green."""
        click.echo(f"{Fore.GREEN}✓ {message}{Style.RESET_ALL}")

    def print_error(self, message: str):
        """Print error message in red."""
        click.echo(f"{Fore.RED}✗ {message}{Style.RESET_ALL}")

    def print_info(self, message: str):
        """Print info message in blue."""
        click.echo(f"{Fore.BLUE}ℹ {message}{Style.RESET_ALL}")

    def print_warning(self, message: str):
        """Print warning message in yellow."""
        click.echo(f"{Fore.YELLOW}⚠ {message}{Style.RESET_ALL}")

    def format_person_table(self, people: List[Dict[str, Any]]) -> str:
        """Format people data as a table."""
        if not people:
            return "No people found."

        headers = ["#", "Name", "Email", "Position", "Department", "Confidence"]
        rows = []

        for i, person in enumerate(people, 1):
            name = f"{person.get('first_name', '')} {person.get('last_name', '')}".strip()
            email = person.get('value', 'N/A')  # Hunter.io uses 'value' for email
            position = person.get('position', 'N/A')
            department = person.get('department', 'N/A')
            confidence = person.get('confidence', 'N/A')

            rows.append([i, name, email, position, department, confidence])

        return tabulate(rows, headers=headers, tablefmt="grid")

    def display_person_details(self, person_data: Dict[str, Any]):
        """Display detailed information about a person."""
        person = person_data.get('data', {})

        if not person:
            self.print_error("No person data found.")
            return

        click.echo(f"\n{Fore.CYAN}{'='*60}")
        click.echo(f"{Fore.CYAN}PERSON DETAILS")
        click.echo(f"{Fore.CYAN}{'='*60}{Style.RESET_ALL}")

        # Personal Information
        click.echo(f"\n{Fore.YELLOW}Personal Information:{Style.RESET_ALL}")
        click.echo(f"  Name: {person.get('first_name', '')} {person.get('last_name', '')}")
        click.echo(f"  Email: {person.get('email', 'N/A')}")
        click.echo(f"  Phone: {person.get('phone_number', 'N/A')}")

        # Professional Information
        click.echo(f"\n{Fore.YELLOW}Professional Information:{Style.RESET_ALL}")
        click.echo(f"  Position: {person.get('position', 'N/A')}")
        click.echo(f"  Department: {person.get('department', 'N/A')}")
        click.echo(f"  Seniority: {person.get('seniority', 'N/A')}")

        # Company Information
        if 'company' in person:
            company = person['company']
            click.echo(f"\n{Fore.YELLOW}Company Information:{Style.RESET_ALL}")
            click.echo(f"  Name: {company.get('name', 'N/A')}")
            click.echo(f"  Domain: {company.get('domain', 'N/A')}")
            click.echo(f"  Industry: {company.get('industry', 'N/A')}")
            click.echo(f"  Size: {company.get('size', 'N/A')}")

        # Social Media
        if 'linkedin_url' in person or 'twitter' in person:
            click.echo(f"\n{Fore.YELLOW}Social Media:{Style.RESET_ALL}")
            if person.get('linkedin_url'):
                click.echo(f"  LinkedIn: {person['linkedin_url']}")
            if person.get('twitter'):
                click.echo(f"  Twitter: {person['twitter']}")

        # Additional Information
        if 'sources' in person:
            click.echo(f"\n{Fore.YELLOW}Sources:{Style.RESET_ALL}")
            for source in person['sources'][:3]:  # Show first 3 sources
                click.echo(f"  - {source.get('domain', 'N/A')} ({source.get('uri', 'N/A')})")


@click.group()
def cli():
    """Hunter.io CLI Tool for Enterprise People Search."""
    pass


@cli.command()
@click.argument('domain')
@click.option('--limit', '-l', default=50, help='Number of results to return (max 100)')
@click.option('--department', '-d', help='Filter by department')
@click.option('--seniority', '-s', help='Filter by seniority level')
@click.option('--interactive', '-i', is_flag=True, help='Interactive mode for selecting employees')
def search(domain: str, limit: int, department: Optional[str], seniority: Optional[str], interactive: bool):
    """Search for people in a company domain."""
    hunter_cli = HunterCLI()

    hunter_cli.print_info(f"Searching for people in domain: {domain}")

    try:
        # Perform domain search
        result = hunter_cli.client.domain_search(
            domain=domain,
            limit=limit,
            department=department,
            seniority=seniority
        )

        data = result.get('data', {})
        people = data.get('emails', [])

        if not people:
            hunter_cli.print_warning("No people found for this domain.")
            return

        # Display company information
        click.echo(f"\n{Fore.CYAN}Company: {data.get('organization', domain)}")
        click.echo(f"Domain: {data.get('domain', domain)}")
        click.echo(f"Industry: {data.get('industry', 'N/A')}{Style.RESET_ALL}")

        # Display results
        click.echo(f"\n{Fore.GREEN}Found {len(people)} people:{Style.RESET_ALL}")
        click.echo(hunter_cli.format_person_table(people))

        # Interactive mode
        if interactive and people:
            click.echo(f"\n{Fore.YELLOW}Interactive Mode:{Style.RESET_ALL}")
            while True:
                try:
                    choice = click.prompt(
                        f"Select a person (1-{len(people)}) or 'q' to quit",
                        type=str
                    )

                    if choice.lower() == 'q':
                        break

                    index = int(choice) - 1
                    if 0 <= index < len(people):
                        selected_person = people[index]
                        email = selected_person.get('value')  # Hunter.io uses 'value' for email

                        if email:
                            hunter_cli.print_info(f"Getting detailed information for {email}...")
                            person_details = hunter_cli.client.person_enrichment(email)
                            hunter_cli.display_person_details(person_details)
                        else:
                            hunter_cli.print_error("No email found for this person.")
                    else:
                        hunter_cli.print_error("Invalid selection. Please try again.")

                except (ValueError, KeyboardInterrupt):
                    hunter_cli.print_info("Exiting interactive mode.")
                    break

    except HunterAPIError as e:
        hunter_cli.print_error(f"API Error: {str(e)}")
        sys.exit(1)
    except Exception as e:
        hunter_cli.print_error(f"Unexpected error: {str(e)}")
        sys.exit(1)


@cli.command()
@click.argument('email')
def details(email: str):
    """Get detailed information about a person by email."""
    hunter_cli = HunterCLI()

    hunter_cli.print_info(f"Getting details for: {email}")

    try:
        result = hunter_cli.client.person_enrichment(email)
        hunter_cli.display_person_details(result)

    except HunterAPIError as e:
        hunter_cli.print_error(f"API Error: {str(e)}")
        sys.exit(1)
    except Exception as e:
        hunter_cli.print_error(f"Unexpected error: {str(e)}")
        sys.exit(1)


@cli.command()
@click.argument('domain')
@click.argument('first_name')
@click.argument('last_name')
def find_email(domain: str, first_name: str, last_name: str):
    """Find email address for a specific person."""
    hunter_cli = HunterCLI()

    hunter_cli.print_info(f"Finding email for: {first_name} {last_name} at {domain}")

    try:
        result = hunter_cli.client.email_finder(domain, first_name, last_name)
        data = result.get('data', {})

        if data.get('email'):
            hunter_cli.print_success(f"Email found: {data['email']}")
            click.echo(f"Confidence: {data.get('confidence', 'N/A')}%")

            # Ask if user wants detailed information
            if click.confirm("Do you want to get detailed information about this person?"):
                person_details = hunter_cli.client.person_enrichment(data['email'])
                hunter_cli.display_person_details(person_details)
        else:
            hunter_cli.print_warning("No email found for this person.")

    except HunterAPIError as e:
        hunter_cli.print_error(f"API Error: {str(e)}")
        sys.exit(1)
    except Exception as e:
        hunter_cli.print_error(f"Unexpected error: {str(e)}")
        sys.exit(1)


@cli.command()
@click.argument('email')
def verify(email: str):
    """Verify an email address."""
    hunter_cli = HunterCLI()

    hunter_cli.print_info(f"Verifying email: {email}")

    try:
        result = hunter_cli.client.email_verifier(email)
        data = result.get('data', {})

        status = data.get('status', 'unknown')
        confidence = data.get('confidence', 'N/A')

        if status == 'valid':
            hunter_cli.print_success(f"Email is valid (Confidence: {confidence}%)")
        elif status == 'invalid':
            hunter_cli.print_error(f"Email is invalid (Confidence: {confidence}%)")
        else:
            hunter_cli.print_warning(f"Email status: {status} (Confidence: {confidence}%)")

        # Additional verification details
        if 'result' in data:
            click.echo(f"\nVerification details:")
            click.echo(f"  Result: {data['result']}")
            click.echo(f"  Score: {data.get('score', 'N/A')}")
            click.echo(f"  Regexp: {data.get('regexp', 'N/A')}")
            click.echo(f"  Gibberish: {data.get('gibberish', 'N/A')}")
            click.echo(f"  Disposable: {data.get('disposable', 'N/A')}")
            click.echo(f"  Webmail: {data.get('webmail', 'N/A')}")

    except HunterAPIError as e:
        hunter_cli.print_error(f"API Error: {str(e)}")
        sys.exit(1)
    except Exception as e:
        hunter_cli.print_error(f"Unexpected error: {str(e)}")
        sys.exit(1)


@cli.command()
@click.argument('domain')
def company(domain: str):
    """Get detailed information about a company."""
    hunter_cli = HunterCLI()

    hunter_cli.print_info(f"Getting company information for: {domain}")

    try:
        result = hunter_cli.client.company_enrichment(domain)
        data = result.get('data', {})

        if not data:
            hunter_cli.print_error("No company data found.")
            return

        click.echo(f"\n{Fore.CYAN}{'='*60}")
        click.echo(f"{Fore.CYAN}COMPANY DETAILS")
        click.echo(f"{Fore.CYAN}{'='*60}{Style.RESET_ALL}")

        click.echo(f"\n{Fore.YELLOW}Basic Information:{Style.RESET_ALL}")
        click.echo(f"  Name: {data.get('name', 'N/A')}")
        click.echo(f"  Domain: {data.get('domain', 'N/A')}")
        click.echo(f"  Industry: {data.get('industry', 'N/A')}")
        click.echo(f"  Size: {data.get('size', 'N/A')}")
        click.echo(f"  Country: {data.get('country', 'N/A')}")

        if 'description' in data:
            click.echo(f"\n{Fore.YELLOW}Description:{Style.RESET_ALL}")
            click.echo(f"  {data['description']}")

        if 'technologies' in data:
            click.echo(f"\n{Fore.YELLOW}Technologies:{Style.RESET_ALL}")
            for tech in data['technologies'][:10]:  # Show first 10 technologies
                click.echo(f"  - {tech}")

    except HunterAPIError as e:
        hunter_cli.print_error(f"API Error: {str(e)}")
        sys.exit(1)
    except Exception as e:
        hunter_cli.print_error(f"Unexpected error: {str(e)}")
        sys.exit(1)


if __name__ == '__main__':
    cli()
